<?php
// إعداد الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "onlinexaminationci";

// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$success = false;
if (isset($_POST['brandlist']) && isset($_POST['questions'])) {
    $exams = $_POST['brandlist'];
    $questions = $_POST['questions'];
    $questionsString = implode(',', $questions);
    
    foreach ($exams as $exam_id) {
        $sql = "UPDATE mo SET questions = '$questionsString' WHERE id_ujian = '$exam_id'";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("is", $exam_id, $questionsString);
            if ($stmt->execute()) {
                $success = true;
            }
            $stmt->close();
        }
    }
}

// إغلاق الاتصال بقاعدة البيانات
$conn->close();
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتيجة الإرسال</title>
    <style>
        .message-box {
            margin: 20px auto;
            padding: 20px;
            text-align: center;
            border: 2px solid #4CAF50;
            border-radius: 5px;
            width: 50%;
            background-color: #f9f9f9;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
            border-color: #f44336;
        }
        .back-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            text-align: center;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>

<div class="message-box <?php echo $success ? 'success' : 'error'; ?>">
    <?php if ($success): ?>
  تم تحديث اسئلة الإمتحان بنجاح
    <?php else: ?>
        لم يتم التحديث
    <?php endif; ?>
</div>

<a class="back-button" href="http://smartexaminationsystm.site/nady8.php">رجوع</a>

</body>
</html>
