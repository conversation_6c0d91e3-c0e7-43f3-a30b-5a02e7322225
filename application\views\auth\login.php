<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول</title>
    <link rel="stylesheet" href="<?=base_url()?>assets/dist/css/bootstrap.min.css">
    <style>
        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            background-size: cover !important;
            background-repeat: no-repeat !important;
            background-position: center center !important;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .login-box-body {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .form-control-feedback {
            right: 10px;
            top: 10px;
            position: absolute;
        }
        #togglePassword {
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
        @media (max-width: 768px) {
            .login-box-body {
                width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="login-box-body col-lg-3 col-md-4 col-sm-10 col-xs-12">
        <h3 class="text-center mt-0 mb-4">
            <b>SM A RT </b> <b>E</b>xamination <b>S</b>ystem
        </h3>
        <p class="login-box-msg">تسجيل الدخول</p>

        <div id="infoMessage" class="text-center"><?php echo $message;?></div>

        <?= form_open("auth/cek_login", array('id'=>'login'));?>
        <div class="form-group has-feedback">
            <input type="text" name="identity" id="identity" class="form-control" placeholder="أدخل بريدك الإلكتروني" autocomplete="off">

            <!-- قائمة اختيار البريد الإلكتروني -->
            <select id="savedIdentitiesSelect" class="form-control mt-2">
                <option value="">-- اختر بريد إلكتروني محفوظ --</option>
            </select>

            <span class="fa fa-envelope form-control-feedback"></span>
            <span class="help-block"></span>
        </div>

        <div class="form-group has-feedback position-relative">
            <input type="password" name="password" id="password" class="form-control" placeholder="أدخل كلمة المرور">
            <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            <span class="help-block"></span>

            <!-- زر عرض/إخفاء كلمة المرور -->
            <button type="button" id="togglePassword" class="btn btn-secondary btn-sm position-absolute">
                <i id="togglePasswordIcon" class="glyphicon glyphicon-eye-open"></i>
            </button>
        </div>

        <div class="row">
            <div class="col-xs-8">
                <div class="checkbox icheck">
                    <label>
                        <?= form_checkbox('remember', '', FALSE, 'id="remember"');?> تذكرني
                    </label>
                </div>
            </div>
            <!-- /.col -->
            <div class="col-xs-4">
                <?= form_submit('submit', lang('login_submit_btn'), array('id'=>'submit','class'=>'btn btn-success btn-block btn-flat'));?>
            </div>
            <!-- /.col -->
        </div>
        <?= form_close(); ?>
    </div>

    <script type="text/javascript">
        let base_url = '<?=base_url();?>';
        let index = new URLSearchParams(window.location.search).get('index');

        document.addEventListener('DOMContentLoaded', function() {
            let savedData = JSON.parse(localStorage.getItem('savedData')) || [];
            let savedIdentitiesSelect = document.getElementById('savedIdentitiesSelect');
            let identityField = document.getElementById('identity');
            let passwordField = document.getElementById('password');

            // إضافة كل بريد إلكتروني إلى قائمة الاختيار
            savedData.forEach(function(data) {
                let option = document.createElement('option');
                option.value = data.identity;
                option.textContent = data.identity;
                savedIdentitiesSelect.appendChild(option);
            });

            // عندما يختار المستخدم بريداً إلكترونياً من القائمة، يتم ملء حقلي الإدخال بالبريد وكلمة المرور المرتبطة
            savedIdentitiesSelect.addEventListener('change', function() {
                let selectedData = savedData.find(data => data.identity === savedIdentitiesSelect.value);
                if (selectedData) {
                    identityField.value = selectedData.identity;
                    passwordField.value = selectedData.password;
                } else {
                    identityField.value = '';
                    passwordField.value = '';
                }
            });

            // ميزة عرض/إخفاء كلمة المرور
            let togglePasswordButton = document.getElementById('togglePassword');
            let togglePasswordIcon = document.getElementById('togglePasswordIcon');

            togglePasswordButton.addEventListener('click', function() {
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    togglePasswordIcon.className = 'glyphicon glyphicon-eye-close';
                } else {
                    passwordField.type = 'password';
                    togglePasswordIcon.className = 'glyphicon glyphicon-eye-open';
                }
            });
        });

        document.getElementById('login').addEventListener('submit', function(e) {
            e.preventDefault(); // إيقاف الإرسال الافتراضي للنموذج

            let identityField = document.getElementById('identity');
            let passwordField = document.getElementById('password');
            let savedData = JSON.parse(localStorage.getItem('savedData')) || [];

            // التحقق من صحة البريد الإلكتروني وكلمة المرور
            validateLogin(identityField.value, passwordField.value).then(isValid => {
                if (isValid) {
                    let existingData = savedData.find(data => data.identity === identityField.value);

                    if (existingData) {
                        existingData.password = passwordField.value;
                    } else {
                        savedData.push({
                            identity: identityField.value,
                            password: passwordField.value
                        });
                    }

                    localStorage.setItem('savedData', JSON.stringify(savedData));

                    document.getElementById('login').submit();
                } else {
                    alert('البريد الإلكتروني أو كلمة المرور غير صحيحة.');
                }
            });
        });

        function validateLogin(email, password) {
            return new Promise(resolve => {
                // يمكنك إضافة منطق للتحقق من صحة البيانات هنا
                resolve(true);
            });
        }
    </script>
    <script src="<?=base_url()?>assets/dist/js/app/auth/login.js"></script>
</body>
</html>
